
import datetime
import boto3
import random
import string
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlalchemy.orm import Session
from models.admin import AdminModel, UsedPasswordTokenModel
from schemas.admin import Admin_cognito_create, AdminUpdate, AdminResponse
from config import settings
from jose import jwt
from models.rbac import RoleModulePermission, Role, Module
from utils.response_utils import (
    success_response, created_response, not_found_response,
    internal_server_error_response, conflict_response
)
from utils.email_service import send_admin_welcome_email, send_admin_welcome_email_with_password_change
from utils.custom_jwt_utils import generate_password_change_token, validate_password_change_token, extract_username_from_token, get_token_hash, get_token_expiration
import logging

logger = logging.getLogger(__name__)

client = boto3.client(
    'cognito-idp',
    region_name="us-east-1",
)

def generate_random_password(length=12):
    if length < 8:
        length = 8

    # Define character sets
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"

    password = [
        random.choice(uppercase),
        random.choice(digits),
        random.choice(special_chars)
    ]

    all_chars = lowercase + uppercase + digits + special_chars
    for _ in range(length - 3):
        password.append(random.choice(all_chars))

    random.shuffle(password)

    return ''.join(password)


def create_admin_cognito(token: str, new_admin: Admin_cognito_create, db):
    try:
        decoded = jwt.get_unverified_claims(token)
        createdBy = decoded.get('sub') or decoded.get('cognitoid') or decoded.get('id')
        updatedBy = createdBy
        if not createdBy:
            raise HTTPException(status_code=400, detail={"message": "Invalid token: missing admin id for audit fields."})
 
        try:
            temp_password = generate_random_password()

            cognito_resp = client.admin_create_user(
                UserPoolId=settings.COGNITO_USER_POOL_ID,
                Username=new_admin.username,
                UserAttributes=[
                    {'Name': 'email', 'Value': new_admin.email},
                    {'Name': 'preferred_username', 'Value': new_admin.username},
                    {"Name": "email_verified", "Value": "true"}
                ],
                TemporaryPassword=temp_password,
                MessageAction='SUPPRESS',
                DesiredDeliveryMediums=[] 
            )
            cognitoId = cognito_resp['User']['Username']
            
            # Generate custom JWT token for password change (24 hours)
            try:
                access_token = generate_password_change_token(new_admin.username, expires_in_hours=24)
            except Exception as e:
                logger.error(f"Failed to generate password change token: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to generate password change token: {str(e)}")
        except client.exceptions.UsernameExistsException:
            raise HTTPException(status_code=400, detail="User already exists in Cognito.")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Cognito sign up failed: {str(e)}")
 
        # new admin in the PGDB
        new_admin_db = AdminModel(
            email=new_admin.email,
            username=new_admin.username,
            firstName=new_admin.firstName,
            lastName=new_admin.lastName,
            phone=new_admin.phone,
            countryCode=new_admin.countryCode,
            cognitoId=cognitoId,
            createdBy=createdBy,
            updatedBy=updatedBy,
            roles=new_admin.roles,
            isActive=True,
            isTempPassword=True,  # Password is temporary - user needs to change it
            emailVerified=True,  # Since we set email_verified=true in Cognito
            lastLogin=datetime.datetime.now(datetime.timezone.utc),
        )
        db.add(new_admin_db)
        db.commit()
        db.refresh(new_admin_db)
        
        # Send welcome email to the new admin with password change link
        try:
            admin_email_data = {
                'email': new_admin_db.email,
                'username': new_admin_db.username,
                'firstName': new_admin_db.firstName if new_admin_db.firstName is not None else '',
                'lastName': new_admin_db.lastName if new_admin_db.lastName is not None else '',
                'roles': new_admin_db.roles if new_admin_db.roles is not None else ['Administrator'],
                'accessToken': access_token
            }
            email_sent = send_admin_welcome_email_with_password_change(admin_email_data)
            
            if email_sent:
                logger.info(f"Welcome email sent successfully to {new_admin_db.email}")
            else:
                logger.warning(f"Failed to send welcome email to {new_admin_db.email}")
                
        except Exception as e:
            logger.error(f"Error sending welcome email to {new_admin_db.email}: {str(e)}")
            # Don't fail the admin creation if email sending fails
        
        return created_response(
            "Admin created in Cognito and DB. Welcome email with password change link sent.",
            {
                "uuid": str(new_admin_db.uuid),
                "username": new_admin_db.username,
                "email": new_admin_db.email,
                "emailSent": locals().get('email_sent', False),
                "isTempPassword": True
            }
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail={"message": f"Error creating admin: {str(e)}"}) 
    
def get_admin_list(db, page: int, pageSize: int):
    try:
        # Get total count
        total_count = db.query(AdminModel).count()
        
        # Get paginated results
        admins = db.query(AdminModel).offset((page - 1) * pageSize).limit(pageSize).all()
        
        # Return empty response with pagination info if no admins found
        if not admins:
            return success_response("No admins found.", {
                "admins": [],
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": page,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": page * pageSize < total_count,
                    "hasPrevious": page > 1
                }
            })
        
        admin_list = []
        for admin in admins:
            admin_list.append({
                "uuid": admin.uuid,
                "username": admin.username,
                "email": admin.email,
                "firstName": admin.firstName,
                "lastName": admin.lastName,
                "phone": admin.phone,
                "countryCode": admin.countryCode,
                "isActive": admin.isActive,
                "isTempPassword": admin.isTempPassword,
                "emailVerified": admin.emailVerified,
                "roles": admin.roles,
                "cognitoId": admin.cognitoId,
                "createdBy": {
                    "uuid": admin.createdBy,
                    # fetch the username of the creator safely
                    "username": (
                        creator.username if (creator := db.query(AdminModel).filter(AdminModel.cognitoId == admin.createdBy).first())
                        else "unknown"
                    ) if admin.createdBy else "unknown"
                },
                "updatedBy": admin.updatedBy,
                "lastLogin": admin.lastLogin,
                "dateCreated": admin.dateCreated,
                "dateUpdated": admin.dateUpdated
            })
        
        return success_response("Admin list fetched successfully.", {
            "admins": admin_list,
            "pagination": {
                "totalCount": total_count,
                "currentPage": page,
                "pageSize": pageSize,
                "totalPages": (total_count + pageSize - 1) // pageSize,
                "hasNext": page * pageSize < total_count,
                "hasPrevious": page > 1
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": f"Error fetching admin list: {str(e)}"})


def get_admin_by_uuid(admin_uuid: str, db):
    """Get admin by UUID"""
    try:
        admin = db.query(AdminModel).filter(AdminModel.uuid == admin_uuid).first()
        if not admin:
            raise HTTPException(status_code=404, detail={"message": f"Admin with uuid {admin_uuid} not found"})

        return success_response(
            "Admin fetched successfully.",
            {"admin": AdminResponse.model_validate(admin)}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": f"Error fetching admin: {str(e)}"})

def get_admin_user(admin_user_payload: dict, db: Session):
    """Get admin user information"""
    try:
        admin = db.query(AdminModel).filter(AdminModel.cognitoId == admin_user_payload["sub"]).first()
        if not admin:
            raise HTTPException(status_code=404, detail={"message": f"Admin not found"})

        role_name = admin.roles[0]
        
        role = db.query(Role).filter(Role.slug == role_name).first()

        if not role:
            raise HTTPException(status_code=404, detail={"message": f"Role '{role_name}' not found for admin."})

        permissions = db.query(RoleModulePermission).filter(RoleModulePermission.roleId == role.id).all()

        modules = db.query(Module).filter(Module.id.in_([perm.moduleId for perm in permissions])).all()

        permissions = [{module.slug: {
            "create": perm.create,
            "update": perm.update,
            "delete": perm.delete,
            "view": perm.view
        }} for module, perm in zip(modules, permissions)]

        return success_response(
            "Admin user and permissions fetched successfully.",
            {
                "user": {
                    "uuid": admin.uuid,
                    "username": admin.username,
                    "email": admin.email,
                    "firstName": admin.firstName,
                    "lastName": admin.lastName,
                    "phone": admin.phone,
                    "countryCode": admin.countryCode,
                    "isActive": admin.isActive,
                    "isTempPassword": admin.isTempPassword,
                    "emailVerified": admin.emailVerified,
                    "roles": role_name,
                    "createdBy": admin.createdBy,
                    "permissions": permissions
                }
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": f"Error fetching admin user: {str(e)}"})

def update_admin(uuid: str, admin: AdminUpdate, db: Session, admin_user_payload: dict):
    try:
        existing_admin = db.query(AdminModel).filter(AdminModel.uuid == uuid).first()  # use uuid instead of admin_uuid for consistency with other functions, but it's not required
        if not existing_admin:
            raise HTTPException(status_code=404, detail="Admin not found")
        
        username = existing_admin.username
        
        if admin.password or admin.email:  # if either password or email is provided, update both in Cognito
            try:
                if admin.email:  # if email is provided, update it in Cognito
                    client.admin_update_user_attributes(
                        UserPoolId=settings.COGNITO_USER_POOL_ID,
                        Username=username,
                        UserAttributes=[
                            {
                                'Name': 'email',
                                'Value': admin.email
                            }
                        ]
                    )
                    
                if admin.password:  # if password is provided, update it in Cognito
                    client.admin_set_user_password(
                        UserPoolId=settings.COGNITO_USER_POOL_ID,
                        Username=username,
                        Password=admin.password,
                        Permanent=True
                    )
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Cognito email update failed: {str(e)}")

        # remove password from update to avoid saving it in the DB
        admin_dict = admin.dict(exclude_unset=True)
        admin_dict.pop("password", None)
        
        admin_dict["updatedBy"] = admin_user_payload["sub"]
        admin_dict["dateUpdated"] = datetime.datetime.now(datetime.timezone.utc)
        
        # Update the existing admin with the new values
        for key, value in admin_dict.items():
            setattr(existing_admin, key, value)

        db.commit()
        db.refresh(existing_admin)

        return success_response(
            "Admin updated successfully",
            {"admin": AdminResponse.model_validate(existing_admin)}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": f"Error updating admin: {str(e)}"})

def delete_admin(uuid: str, db: Session, admin_user_payload: dict):
    """Delete admin from both database and Cognito"""
    try:
        # Find the admin to delete
        admin_to_delete = db.query(AdminModel).filter(AdminModel.uuid == uuid).first()
        if not admin_to_delete:
            raise HTTPException(status_code=404, detail={"message": f"Admin with uuid {uuid} not found"})
        
        username = admin_to_delete.username
        
        # Delete from Cognito first
        try:
            client.admin_delete_user(
                UserPoolId=settings.COGNITO_USER_POOL_ID,
                Username=username
            )
        except client.exceptions.UserNotFoundException:
            # User might already be deleted from Cognito, continue with DB deletion
            pass
        except Exception as e:
            raise HTTPException(status_code=500, detail={"message": f"Cognito deletion failed: {str(e)}"})
        
        # Delete from database
        db.delete(admin_to_delete)
        db.commit()
        
        return success_response(
            f"Admin '{username}' deleted successfully from both Cognito and database."
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail={"message": f"Error deleting admin: {str(e)}"})


def initiate_forgot_password(username: str, db: Session):
    """Initiate forgot password flow for admin user"""
    try:
        # First, check if the admin exists in our database
        admin = db.query(AdminModel).filter(
            (AdminModel.username == username) | (AdminModel.email == username)
        ).first()
        
        if not admin:
            raise HTTPException(status_code=404, detail={"message": "Admin not found"})
        
        # Use the actual username (not email) for Cognito operations
        cognito_username = admin.username
        
        try:
            # Initiate forgot password with Cognito
            response = client.forgot_password(
                ClientId=settings.COGNITO_CLIENT_ID,
                Username=cognito_username
            )
            
            return success_response(
                "Password reset code sent successfully. Please check your email.",
                {
                    "username": cognito_username,
                    "codeDeliveryDetails": {
                        "destination": response.get('CodeDeliveryDetails', {}).get('Destination', ''),
                        "deliveryMedium": response.get('CodeDeliveryDetails', {}).get('DeliveryMedium', 'EMAIL')
                    }
                }
            )
            
        except client.exceptions.UserNotFoundException:
            raise HTTPException(status_code=404, detail={"message": "Admin not found in Cognito"})
        except client.exceptions.InvalidParameterException as e:
            raise HTTPException(status_code=400, detail={"message": f"Invalid request: {str(e)}"})
        except client.exceptions.LimitExceededException:
            raise HTTPException(status_code=429, detail={"message": "Too many requests. Please try again later."})
        except Exception as e:
            raise HTTPException(status_code=500, detail={"message": f"Cognito forgot password failed: {str(e)}"})
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": f"Error initiating forgot password: {str(e)}"})


def confirm_forgot_password(username: str, confirmation_code: str, new_password: str, db: Session):
    """Confirm forgot password with verification code and set new password"""
    try:
        # Check if the admin exists in our database
        admin = db.query(AdminModel).filter(
            (AdminModel.username == username) | (AdminModel.email == username)
        ).first()
        
        if not admin:
            raise HTTPException(status_code=404, detail={"message": "Admin not found"})
        
        # Use the actual username (not email) for Cognito operations
        cognito_username = admin.username
        
        try:
            # Confirm forgot password with Cognito
            client.confirm_forgot_password(
                ClientId=settings.COGNITO_CLIENT_ID,
                Username=cognito_username,
                ConfirmationCode=confirmation_code,
                Password=new_password
            )
            # Update admin in database to reflect password change
            setattr(admin, "isTempPassword", False)
            setattr(admin, "updatedBy", admin.cognitoId)  # Self-updated
            setattr(admin, "dateUpdated", datetime.datetime.now(datetime.timezone.utc))
            
            db.commit()
            
            return success_response(
                "Password reset successful. You can now login with your new password.",
                {
                    "username": cognito_username,
                    "passwordChanged": True
                }
            )
            
        except client.exceptions.CodeMismatchException:
            raise HTTPException(status_code=400, detail={"message": "Invalid verification code"})
        except client.exceptions.ExpiredCodeException:
            raise HTTPException(status_code=400, detail={"message": "Verification code has expired. Please request a new code."})
        except client.exceptions.InvalidPasswordException as e:
            raise HTTPException(status_code=400, detail={"message": f"Invalid password: {str(e)}"})
        except client.exceptions.UserNotFoundException:
            raise HTTPException(status_code=404, detail={"message": "Admin not found in Cognito"})
        except client.exceptions.InvalidParameterException as e:
            raise HTTPException(status_code=400, detail={"message": f"Invalid request: {str(e)}"})
        except Exception as e:
            raise HTTPException(status_code=500, detail={"message": f"Cognito password confirmation failed: {str(e)}"})
            
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail={"message": f"Error confirming forgot password: {str(e)}"})


def change_admin_password(access_token: str, new_password: str, db: Session):
    """
    Change admin password using custom JWT token with database tracking
    """
    try:
        # Validate custom JWT token and extract username
        payload = validate_password_change_token(access_token)
        if not payload:
            raise HTTPException(status_code=401, detail={"message": "Link is expired"})
        
        username = payload.get("username")
        if not username:
            raise HTTPException(status_code=400, detail={"message": "Invalid token: missing username"})
        
        # Check if token has already been used
        token_hash = get_token_hash(access_token)
        used_token = db.query(UsedPasswordTokenModel).filter(UsedPasswordTokenModel.token_hash == token_hash).first()
        if used_token:
            raise HTTPException(status_code=400, detail={"message": "You already changed the password"})
        
        # Check if admin exists in database
        admin = db.query(AdminModel).filter(AdminModel.username == username).first()
        if not admin:
            raise HTTPException(status_code=404, detail={"message": "Admin not found"})
        
        # Check if admin has temporary password
        if not getattr(admin, "isTempPassword"):
            raise HTTPException(status_code=400, detail={"message": "Password has already been changed"})
        
        try:
            # Update password in Cognito
            client.admin_set_user_password(
                UserPoolId=settings.COGNITO_USER_POOL_ID,
                Username=username,
                Password=new_password,
                Permanent=True
            )
            
            # Update database to reflect password change
            setattr(admin, "isTempPassword", False)
            setattr(admin, "updatedBy", admin.cognitoId)  # Self-updated
            setattr(admin, "dateUpdated", datetime.datetime.now(datetime.timezone.utc))
            
            # Store used token in database to prevent reuse
            token_expiration = get_token_expiration(access_token)
            used_token_record = UsedPasswordTokenModel(
                token_hash=token_hash,
                username=username,
                expires_at=token_expiration
            )
            db.add(used_token_record)
            
            db.commit()
            
            return success_response(
                "Password changed successfully. You can now login with your new password.",
                {
                    "username": username,
                    "passwordChanged": True,
                    "isTempPassword": False
                }
            )
            
        except client.exceptions.UserNotFoundException:
            raise HTTPException(status_code=404, detail={"message": "Admin not found in Cognito"})
        except client.exceptions.InvalidPasswordException as e:
            raise HTTPException(status_code=400, detail={"message": f"Invalid password: {str(e)}"})
        except Exception as e:
            raise HTTPException(status_code=500, detail={"message": f"Cognito password update failed: {str(e)}"})
            
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail={"message": f"Error changing password: {str(e)}"})
    
def delete_token_hash(username: str, db: Session):
    try:
        db.query(UsedPasswordTokenModel).filter(UsedPasswordTokenModel.username == username).delete()
        
        admin = db.query(AdminModel).filter(AdminModel.username == username).first()
        
        setattr(admin, "isTempPassword", True)
        setattr(admin, "updatedBy", "testing")
        setattr(admin, "dateUpdated", datetime.datetime.now(datetime.timezone.utc))
        
        db.commit()
        return success_response("Token hash Deleted successfully")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail={"message": f"Error deleting token hash: {str(e)}"})